rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Allow read access to chatbot collections for everyone
    match /chatbot_pages/{document} {
      allow read: if true;
      allow write: if true; // For crawling/indexing
    }
    
    match /chatbot_chunks/{document} {
      allow read: if true;
      allow write: if true; // For crawling/indexing
    }
    
    // Existing rules for other collections
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    match /pdfs/{document} {
      allow read, write: if request.auth != null;
    }
    
    match /scenarios/{document} {
      allow read, write: if request.auth != null;
    }
    
    // Allow all other documents to be read/written by authenticated users
    match /{document=**} {
      allow read, write: if request.auth != null;
    }
  }
}
