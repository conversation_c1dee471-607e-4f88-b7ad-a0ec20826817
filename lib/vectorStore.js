/**
 * Vector Store Utilities
 * Handles embeddings generation and similarity search
 */

import { pipeline } from '@xenova/transformers';
import { db } from './firebase';
import { collection, doc, setDoc, getDocs, query, where, orderBy } from 'firebase/firestore';

// Initialize the embedding pipeline
let embeddingPipeline = null;

/**
 * Initialize the embedding model
 */
async function initEmbeddingModel() {
  if (!embeddingPipeline) {
    console.log('Loading embedding model...');
    embeddingPipeline = await pipeline(
      'feature-extraction',
      'Xenova/all-MiniLM-L6-v2',
      { device: 'cpu' }
    );
    console.log('Embedding model loaded');
  }
  return embeddingPipeline;
}

/**
 * Generate embeddings for text
 */
export async function generateEmbedding(text) {
  try {
    const model = await initEmbeddingModel();
    const output = await model(text, { pooling: 'mean', normalize: true });
    return Array.from(output.data);
  } catch (error) {
    console.error('Error generating embedding:', error);
    throw error;
  }
}

/**
 * Calculate cosine similarity between two vectors
 */
export function cosineSimilarity(vecA, vecB) {
  if (vecA.length !== vecB.length) {
    throw new Error('Vectors must have the same length');
  }

  let dotProduct = 0;
  let normA = 0;
  let normB = 0;

  for (let i = 0; i < vecA.length; i++) {
    dotProduct += vecA[i] * vecB[i];
    normA += vecA[i] * vecA[i];
    normB += vecB[i] * vecB[i];
  }

  return dotProduct / (Math.sqrt(normA) * Math.sqrt(normB));
}

/**
 * Store page content with embeddings in Firestore
 */
export async function storePageContent(pageData) {
  try {
    const { url, content, metadata, chunks } = pageData;

    // Generate embeddings for each chunk
    const chunksWithEmbeddings = [];

    for (let i = 0; i < chunks.length; i++) {
      const chunk = chunks[i];
      if (chunk && chunk.trim().length > 0) {
        console.log(`Generating embedding for chunk ${i + 1}/${chunks.length} of ${url}`);
        const embedding = await generateEmbedding(chunk);

        chunksWithEmbeddings.push({
          id: `${url}_chunk_${i}`,
          url,
          content: chunk,
          embedding,
          chunkIndex: i,
          createdAt: new Date().toISOString()
        });
      }
    }

    // Store page metadata
    const pageDoc = {
      url,
      title: metadata.title,
      description: metadata.description,
      keywords: metadata.keywords,
      headings: metadata.headings,
      fullContent: content,
      chunkCount: chunksWithEmbeddings.length,
      lastIndexed: new Date().toISOString(),
      status: 'indexed'
    };

    // Save to Firestore
    const pageId = btoa(url).replace(/[^a-zA-Z0-9]/g, ''); // Create safe document ID
    await setDoc(doc(db, 'chatbot_pages', pageId), pageDoc);

    // Save chunks with embeddings
    for (const chunk of chunksWithEmbeddings) {
      const chunkId = btoa(chunk.id).replace(/[^a-zA-Z0-9]/g, '');
      await setDoc(doc(db, 'chatbot_chunks', chunkId), chunk);
    }

    console.log(`Stored ${chunksWithEmbeddings.length} chunks for ${url}`);
    return {
      success: true,
      pageId,
      chunkCount: chunksWithEmbeddings.length
    };
  } catch (error) {
    console.error('Error storing page content:', error);
    throw error;
  }
}

/**
 * Search for relevant content based on query
 */
export async function searchRelevantContent(query, maxResults = 5) {
  try {
    console.log(`Searching for: "${query}"`);

    // Check if Firebase is available
    if (!db) {
      console.warn('Firebase not available, returning empty results');
      return [];
    }

    // Generate embedding for the query
    const queryEmbedding = await generateEmbedding(query);

    // Get all chunks from Firestore
    const chunksSnapshot = await getDocs(collection(db, 'chatbot_chunks'));
    const chunks = [];

    chunksSnapshot.forEach(doc => {
      const data = doc.data();
      if (data.embedding && data.content) {
        chunks.push({
          id: doc.id,
          ...data
        });
      }
    });

    console.log(`Found ${chunks.length} chunks to search`);

    // Calculate similarities
    const similarities = chunks.map(chunk => {
      const similarity = cosineSimilarity(queryEmbedding, chunk.embedding);
      return {
        ...chunk,
        similarity
      };
    });

    // Sort by similarity and return top results
    const topResults = similarities
      .sort((a, b) => b.similarity - a.similarity)
      .slice(0, maxResults)
      .filter(result => result.similarity > 0.3); // Filter out very low similarity results

    console.log(`Returning ${topResults.length} relevant chunks`);
    return topResults;
  } catch (error) {
    console.error('Error searching content:', error);
    throw error;
  }
}

/**
 * Enhanced search with keyword matching and semantic search
 */
export async function searchRelevantContentEnhanced(query, maxResults = 5) {
  try {
    console.log(`Enhanced search for: "${query}"`);

    // Check if Firebase is available
    if (!db) {
      console.warn('Firebase not available, falling back to basic search');
      return await searchRelevantContent(query, maxResults);
    }

    // Get semantic results
    const semanticResults = await searchRelevantContent(query, maxResults * 2);

    // Get all chunks for keyword search
    const chunksSnapshot = await getDocs(collection(db, 'chatbot_chunks'));
    const allChunks = [];

    chunksSnapshot.forEach(doc => {
      const data = doc.data();
      if (data.content) {
        allChunks.push({
          id: doc.id,
          ...data
        });
      }
    });

    // Keyword search
    const queryWords = query.toLowerCase().split(/\s+/).filter(word => word.length > 2);
    const keywordResults = allChunks.map(chunk => {
      const content = chunk.content.toLowerCase();
      let keywordScore = 0;

      queryWords.forEach(word => {
        const matches = (content.match(new RegExp(word, 'g')) || []).length;
        keywordScore += matches;
      });

      return {
        ...chunk,
        keywordScore: keywordScore / queryWords.length
      };
    }).filter(result => result.keywordScore > 0);

    // Combine and deduplicate results
    const combinedResults = new Map();

    // Add semantic results
    semanticResults.forEach(result => {
      combinedResults.set(result.id, {
        ...result,
        semanticScore: result.similarity,
        keywordScore: 0
      });
    });

    // Add keyword results
    keywordResults.forEach(result => {
      if (combinedResults.has(result.id)) {
        combinedResults.get(result.id).keywordScore = result.keywordScore;
      } else {
        combinedResults.set(result.id, {
          ...result,
          semanticScore: 0
        });
      }
    });

    // Calculate combined score and sort
    const finalResults = Array.from(combinedResults.values())
      .map(result => ({
        ...result,
        combinedScore: (result.semanticScore * 0.7) + (result.keywordScore * 0.3)
      }))
      .sort((a, b) => b.combinedScore - a.combinedScore)
      .slice(0, maxResults);

    console.log(`Enhanced search returning ${finalResults.length} results`);

    return finalResults;

  } catch (error) {
    console.error('Error in enhanced search:', error);
    return await searchRelevantContent(query, maxResults); // Fallback to basic search
  }
}

/**
 * Get all indexed pages
 */
export async function getIndexedPages() {
  try {
    const pagesSnapshot = await getDocs(
      query(
        collection(db, 'chatbot_pages'),
        orderBy('lastIndexed', 'desc')
      )
    );

    const pages = [];
    pagesSnapshot.forEach(doc => {
      pages.push({
        id: doc.id,
        ...doc.data()
      });
    });

    return pages;
  } catch (error) {
    console.error('Error getting indexed pages:', error);
    throw error;
  }
}

/**
 * Delete indexed content for a URL
 */
export async function deleteIndexedContent(url) {
  try {
    const pageId = btoa(url).replace(/[^a-zA-Z0-9]/g, '');

    // Delete page document
    await deleteDoc(doc(db, 'chatbot_pages', pageId));

    // Delete associated chunks
    const chunksSnapshot = await getDocs(
      query(
        collection(db, 'chatbot_chunks'),
        where('url', '==', url)
      )
    );

    const deletePromises = [];
    chunksSnapshot.forEach(doc => {
      deletePromises.push(deleteDoc(doc.ref));
    });

    await Promise.all(deletePromises);

    console.log(`Deleted indexed content for ${url}`);
    return { success: true };
  } catch (error) {
    console.error('Error deleting indexed content:', error);
    throw error;
  }
}

/**
 * Get statistics about indexed content
 */
export async function getIndexStats() {
  try {
    const [pagesSnapshot, chunksSnapshot] = await Promise.all([
      getDocs(collection(db, 'chatbot_pages')),
      getDocs(collection(db, 'chatbot_chunks'))
    ]);

    return {
      totalPages: pagesSnapshot.size,
      totalChunks: chunksSnapshot.size,
      lastUpdated: new Date().toISOString()
    };
  } catch (error) {
    console.error('Error getting index stats:', error);
    throw error;
  }
}
