/**
 * Chatbot Service
 * Handles AI-powered responses based on website content
 */

import { GoogleGenAI } from '@google/genai';
import { searchRelevantContent, searchRelevantContentEnhanced } from './vectorStore.js';
import { crawlWebsite } from './crawler.js';
import { storePageContent } from './vectorStore.js';

/**
 * Generate AI response based on user query and relevant content
 */
export async function generateChatbotResponse(userQuery, relevantContent = []) {
  try {
    const apiKey = process.env.GEMINI_API_KEY;
    if (!apiKey) {
      throw new Error('Gemini API key not configured');
    }

    const genAI = new GoogleGenAI({ apiKey });

    // Prepare context from relevant content
    const context = relevantContent
      .map(chunk => `Source: ${chunk.url}\nContent: ${chunk.content}`)
      .join('\n\n---\n\n');

    const systemPrompt = relevantContent.length > 0
    ? `You are a helpful AI assistant. Your role is to answer questions about the website's content, features, and services based on the provided context.

Guidelines:
- Answer questions directly and helpfully
- Use the provided context to give accurate information
- If you don't have enough information in the context, say so politely
- Keep responses conversational but informative
- Reference specific pages or sections when relevant

Context from website:
${context}

User Question: ${userQuery}

Please provide a helpful response based on the context above.`
    : `You are a helpful AI assistant for this website. 

Since I don't have access to the full website content right now, I can only provide general information.

User Question: ${userQuery}

Please provide a helpful response based on what you can determine from the question.`;

    // Fix: Use the correct API method based on the generate-scenario example
    const model = genAI.models.generateContent;
    
    const result = await model({
      model: 'gemini-2.0-flash-001',
      contents: [{ role: 'user', parts: [{ text: systemPrompt }] }],
      config: {
        temperature: 0.7,
        maxOutputTokens: 1000,
      },
    });

    let response;
    try {
      // Try different response structures
      response = result.response?.text?.() ||
                result.response?.candidates?.[0]?.content?.parts?.[0]?.text ||
                result.response?.candidates?.[0]?.output ||
                result.response?.text ||
                result.text;
    } catch (error) {
      console.error('Error extracting response text:', error);
      response = null;
    }

    if (!response) {
      console.error('Full API response:', JSON.stringify(result, null, 2));
      throw new Error('No response generated from AI');
    }

    return {
      success: true,
      response: response.trim(),
      sources: relevantContent.map(chunk => ({
        url: chunk.url,
        title: chunk.title || 'Untitled',
        snippet: chunk.content.substring(0, 150) + '...'
      }))
    };

  } catch (error) {
    console.error('Error generating chatbot response:', error);
    return {
      success: false,
      error: error.message || 'Failed to generate response'
    };
  }
}

/**
 * Process user query and generate response
 */
export async function processChatQuery(userQuery) {
  try {
    console.log(`Processing chat query: "${userQuery}"`);

    // Search for relevant content using enhanced search
    let relevantContent = [];
    try {
      relevantContent = await searchRelevantContentEnhanced(userQuery, 5);
      console.log(`Found ${relevantContent.length} relevant content chunks`);
    } catch (searchError) {
      console.warn('Search failed, proceeding with empty context:', searchError.message);
      // Continue with empty content - AI can still provide general responses
    }

    // Generate AI response
    const aiResponse = await generateChatbotResponse(userQuery, relevantContent);

    if (!aiResponse.success) {
      throw new Error(aiResponse.error);
    }

    return {
      success: true,
      response: aiResponse.response,
      sources: aiResponse.sources,
      relevantChunks: relevantContent.length
    };

  } catch (error) {
    console.error('Error processing chat query:', error);

    // Enhanced fallback response based on error type
    let fallbackResponse = "I'm sorry, I'm having trouble processing your question right now.";

    if (error.message.includes('API key')) {
      fallbackResponse = "I'm sorry, the AI service is not properly configured. Please contact the administrator.";
    } else if (error.message.includes('Firebase') || error.message.includes('permission')) {
      fallbackResponse = "I'm having trouble accessing the knowledge base. I can still try to help with general questions about MoneyTales.";
    }

    return {
      success: false,
      response: fallbackResponse + " Please try again later or contact support if the issue persists.",
      error: error.message,
      sources: []
    };
  }
}

/**
 * Crawl and index website content
 */
export async function crawlAndIndexWebsite(startUrl, options = {}) {
  try {
    console.log(`Starting website crawl from: ${startUrl}`);

    const defaultOptions = {
      maxPages: 50,
      delay: 1000,
      maxDepth: 3,
      excludePatterns: [
        '/api/',
        '/admin/',
        '/_next/',
        '/static/',
        '/images/',
        '/css/',
        '/js/',
        '.pdf',
        '.jpg',
        '.png',
        '.gif'
      ]
    };

    const crawlOptions = { ...defaultOptions, ...options };

    // Crawl the website
    const crawlResults = await crawlWebsite(startUrl, crawlOptions);

    console.log(`Crawled ${crawlResults.length} pages`);

    // Store content with embeddings
    const indexResults = [];
    let successCount = 0;
    let errorCount = 0;

    for (const pageData of crawlResults) {
      if (pageData.success && pageData.content.trim().length > 100) {
        try {
          await storePageContent(pageData);
          indexResults.push({
            url: pageData.url,
            status: 'indexed',
            chunkCount: pageData.chunks.length
          });
          successCount++;
          console.log(`Indexed: ${pageData.url}`);
        } catch (error) {
          console.error(`Error indexing ${pageData.url}:`, error);
          indexResults.push({
            url: pageData.url,
            status: 'error',
            error: error.message
          });
          errorCount++;
        }
      } else {
        indexResults.push({
          url: pageData.url,
          status: 'skipped',
          reason: pageData.success ? 'Content too short' : pageData.error
        });
      }
    }

    return {
      success: true,
      crawled: crawlResults.length,
      indexed: successCount,
      errors: errorCount,
      results: indexResults,
      startUrl,
      completedAt: new Date().toISOString()
    };

  } catch (error) {
    console.error('Error crawling and indexing website:', error);
    return {
      success: false,
      error: error.message,
      startUrl,
      completedAt: new Date().toISOString()
    };
  }
}

/**
 * Get chatbot statistics
 */
export async function getChatbotStats() {
  try {
    // This would typically query your database for stats
    // For now, return basic info
    return {
      success: true,
      stats: {
        totalPages: 0, // Would be fetched from database
        totalChunks: 0, // Would be fetched from database
        lastCrawl: null, // Would be fetched from database
        status: 'ready'
      }
    };
  } catch (error) {
    console.error('Error getting chatbot stats:', error);
    return {
      success: false,
      error: error.message
    };
  }
}
